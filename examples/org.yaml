---
# https://backstage.io/docs/features/software-catalog/descriptor-format#kind-user
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: guest
  annotations:
    backstage.io/managed-by-location: url:file://examples/org.yaml
spec:
  memberOf: [guests]
---
# https://backstage.io/docs/features/software-catalog/descriptor-format#kind-group
apiVersion: backstage.io/v1alpha1
kind: Group
metadata:
  name: guests
  annotations:
    backstage.io/managed-by-location: url:file://examples/org.yaml
spec:
  type: team
  children: []

---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: InduwaraSMPN
  annotations:
    backstage.io/managed-by-location: url:file://examples/org.yaml
    google.com/email: <EMAIL>
spec:
  profile:
    email: <EMAIL>
  memberOf: [guests]

---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: jdoe
  annotations:
    backstage.io/managed-by-location: url:file://examples/org.yaml
    google.com/email: <EMAIL>
spec:
  profile:
    displayName: Jenny Doe
    email: <EMAIL>
    picture: https://example.com/staff/jenny-with-party-hat.jpeg
  memberOf: [guests]